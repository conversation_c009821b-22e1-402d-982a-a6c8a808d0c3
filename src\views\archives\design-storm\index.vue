<template>
  <div class="tab-table-panel">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleExport" icon="download" :loading="exportLoading">
              导出
            </a-button>
          </div>
        </VxeTable>

        <!-- 新增弹窗 -->
        <FormModal
          v-if="showFormModal"
          ref="formModalRef"
          @ok="onOperationComplete"
          @close="showFormModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import FormModal from './modules/FormModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'DesignStorm',
    components: {
      VxeTable,
      VxeTableForm,
      FormModal,
    },
    data() {
      return {
        showFormModal: false,
        exportLoading: false,

        list: [],
        tableTitle: '桃花江水库设计暴雨',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 60 },
          {
            title: '阶段',
            field: 'stage',
            width: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                // 每组数据的第一行显示阶段名称，其他行为空
                const groupIndex = Math.floor(rowIndex / 3)
                const inGroupIndex = rowIndex % 3
                return inGroupIndex === 0 ? row.stage : ''
              },
            },
          },
          {
            title: '频率',
            field: 'frequency',
            width: 100,
          },
          {
            title: 'H1点',
            field: 'h1Point',
            width: 80,
          },
          {
            title: 'H3点',
            field: 'h3Point',
            width: 80,
          },
          {
            title: 'H6点',
            field: 'h6Point',
            width: 80,
          },
          {
            title: 'H12点',
            field: 'h12Point',
            width: 80,
          },
          {
            title: 'H24点',
            field: 'h24Point',
            width: 80,
          },
          {
            title: 'H1面',
            field: 'h1Area',
            width: 80,
          },
          {
            title: 'H3面',
            field: 'h3Area',
            width: 80,
          },
          {
            title: 'H6面',
            field: 'h6Area',
            width: 80,
          },
          {
            title: 'H12面',
            field: 'h12Area',
            width: 80,
          },
          {
            title: 'H24面',
            field: 'h24Area',
            width: 80,
          },
          {
            title: '设计暴雨时程分配',
            field: 'fileName',
            width: 150,
            slots: {
              default: ({ row, rowIndex }) => {
                // 每组数据的第一行显示文件名，其他行为空
                const inGroupIndex = rowIndex % 3
                return inGroupIndex === 0 ? row.fileName : ''
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 120,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                // 每组数据的第一行显示操作按钮，其他行为空
                const inGroupIndex = rowIndex % 3
                if (inGroupIndex === 0) {
                  return (
                    <span>
                      <a onClick={() => this.handleEdit(row)}>编辑</a>
                      <a-divider type="vertical" />
                      <a onClick={() => this.handleDelete(row)} style="color: #ff4d4f">删除</a>
                    </span>
                  )
                }
                return ''
              }
            }
          }
        ]
      }
    },
    created() {
      this.getList()
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })

        // 模拟测试数据
        setTimeout(() => {
          this.list = this.generateTestData()
          this.total = this.list.length
          this.loading = false
        }, 500)
      },

      // 生成测试数据
      generateTestData() {
        const stages = ['施工期', '运行期']
        const frequencies = ['P=3.33%', 'P=2%', 'P=0.2%']
        const testData = []

        stages.forEach((stage, stageIndex) => {
          frequencies.forEach((frequency, freqIndex) => {
            testData.push({
              id: stageIndex * 3 + freqIndex + 1,
              stage: stage,
              frequency: frequency,
              h1Point: (Math.random() * 50 + 20).toFixed(2),
              h3Point: (Math.random() * 80 + 30).toFixed(2),
              h6Point: (Math.random() * 120 + 50).toFixed(2),
              h12Point: (Math.random() * 180 + 80).toFixed(2),
              h24Point: (Math.random() * 250 + 120).toFixed(2),
              h1Area: (Math.random() * 45 + 18).toFixed(2),
              h3Area: (Math.random() * 75 + 28).toFixed(2),
              h6Area: (Math.random() * 115 + 48).toFixed(2),
              h12Area: (Math.random() * 175 + 78).toFixed(2),
              h24Area: (Math.random() * 245 + 118).toFixed(2),
              fileName: freqIndex === 0 ? `${stage}_设计暴雨时程分配.xlsx` : '',
              groupId: stageIndex + 1, // 用于标识同一组数据
            })
          })
        })

        return testData
      },

      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 新增
      handleAdd() {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.add())
      },

      // 编辑
      handleEdit(record) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.edit(record))
      },

      // 删除
      handleDelete(record) {
        this.$confirm({
          title: '确认删除',
          content: `确定要删除阶段"${record.stage}"的设计暴雨数据吗？`,
          onOk: () => {
            this.$message.success('删除成功')
            this.getList()
          },
        })
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        setTimeout(() => {
          this.$message.success('导出成功')
          this.exportLoading = false
        }, 2000)
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      }
    }
  }
</script>

<style lang="less" scoped>
  .tab-table-panel {
    height: 100%;
  }
</style>
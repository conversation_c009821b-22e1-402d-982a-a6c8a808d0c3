<template>
  <div class="design-storm-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="page-title">桃花江水库设计暴雨</div>
      <div class="actions">
        <a-button type="primary" icon="plus" @click="showAddModal">添加</a-button>
        <a-button icon="export" @click="handleExport">导出</a-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="false"
        :scroll="{ x: 'max-content' }"
        :merge-cells="mergeCells"
        rowKey="id"
      >
        <span slot="frequency" slot-scope="text">{{ text }}</span>
        <span slot="h1Point" slot-scope="text">{{ text }}</span>
        <span slot="h3Point" slot-scope="text">{{ text }}</span>
        <span slot="h6Point" slot-scope="text">{{ text }}</span>
        <span slot="h12Point" slot-scope="text">{{ text }}</span>
        <span slot="h24Point" slot-scope="text">{{ text }}</span>
        <span slot="h1Area" slot-scope="text">{{ text }}</span>
        <span slot="h3Area" slot-scope="text">{{ text }}</span>
        <span slot="h6Area" slot-scope="text">{{ text }}</span>
        <span slot="h12Area" slot-scope="text">{{ text }}</span>
        <span slot="h24Area" slot-scope="text">{{ text }}</span>
        <span slot="allocation" slot-scope="text">{{ text }}</span>
        <div slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定要删除这项数据吗？" @confirm="handleDelete(record)">
            <a href="#">删除</a>
          </a-popconfirm>
        </div>
      </a-table>
    </div>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirm-loading="modalConfirmLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="800px"
    >
      <a-form :form="form" layout="vertical">
        <!-- 阶段输入框 -->
        <a-form-item label="阶段">
          <a-input
            v-decorator="['stage', { rules: [{ required: true, message: '请输入阶段' }] }]"
            placeholder="请输入阶段"
          />
        </a-form-item>

        <!-- 设计暴雨参数标题 -->
        <div class="parameter-title">设计暴雨参数</div>

        <!-- 参数表格 -->
        <a-table
          :columns="parameterColumns"
          :data-source="parameterData"
          :pagination="false"
          size="small"
          :scroll="{ x: 'max-content' }"
        >
          <template v-for="col in ['p333', 'p2', 'p02']" :slot="col" slot-scope="text, record, index">
            <a-input
              :key="col"
              :value="text"
              @change="e => handleParameterChange(e.target.value, record.key, col)"
              placeholder="请输入数值"
            />
          </template>
        </a-table>

        <!-- 设计暴雨时程分配 -->
        <div class="allocation-section">
          <div class="allocation-title">设计暴雨时程分配</div>
          <div class="file-upload">
            <a-upload
              :file-list="fileList"
              :before-upload="beforeUpload"
              :remove="handleRemove"
            >
              <a-button>
                <a-icon type="upload" /> 选择文件
              </a-button>
            </a-upload>
          </div>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'DesignStorm',
  data() {
    return {
      // 表格列定义
      columns: [
        { title: '序号', dataIndex: 'index', key: 'index', width: 60, align: 'center', customRender: (text, record, index) => this.calculateIndex(index) },
        { title: '阶段', dataIndex: 'stage', key: 'stage', width: 100 },
        { title: '频率', dataIndex: 'frequency', key: 'frequency', width: 100, scopedSlots: { customRender: 'frequency' } },
        { title: 'H1点', dataIndex: 'h1Point', key: 'h1Point', width: 100, scopedSlots: { customRender: 'h1Point' } },
        { title: 'H3点', dataIndex: 'h3Point', key: 'h3Point', width: 100, scopedSlots: { customRender: 'h3Point' } },
        { title: 'H6点', dataIndex: 'h6Point', key: 'h6Point', width: 100, scopedSlots: { customRender: 'h6Point' } },
        { title: 'H12点', dataIndex: 'h12Point', key: 'h12Point', width: 100, scopedSlots: { customRender: 'h12Point' } },
        { title: 'H24点', dataIndex: 'h24Point', key: 'h24Point', width: 100, scopedSlots: { customRender: 'h24Point' } },
        { title: 'H1面', dataIndex: 'h1Area', key: 'h1Area', width: 100, scopedSlots: { customRender: 'h1Area' } },
        { title: 'H3面', dataIndex: 'h3Area', key: 'h3Area', width: 100, scopedSlots: { customRender: 'h3Area' } },
        { title: 'H6面', dataIndex: 'h6Area', key: 'h6Area', width: 100, scopedSlots: { customRender: 'h6Area' } },
        { title: 'H12面', dataIndex: 'h12Area', key: 'h12Area', width: 100, scopedSlots: { customRender: 'h12Area' } },
        { title: 'H24面', dataIndex: 'h24Area', key: 'h24Area', width: 100, scopedSlots: { customRender: 'h24Area' } },
        { title: '设计暴雨时程分配', dataIndex: 'allocation', key: 'allocation', width: 150, scopedSlots: { customRender: 'allocation' } },
        { title: '操作', key: 'action', width: 150, scopedSlots: { customRender: 'action' }, fixed: 'right' }
      ],
      
      // 合并单元格定义
      mergeCells: [],
      
      // 参数表格列定义
      parameterColumns: [
        { title: '项目', dataIndex: 'item', key: 'item' },
        { title: 'P=3.33%', dataIndex: 'p333', key: 'p333', scopedSlots: { customRender: 'p333' } },
        { title: 'P=2%', dataIndex: 'p2', key: 'p2', scopedSlots: { customRender: 'p2' } },
        { title: 'P=0.2%', dataIndex: 'p02', key: 'p02', scopedSlots: { customRender: 'p02' } }
      ],
      
      // 表格数据
      dataSource: [],
      
      // 参数表格数据
      parameterData: [
        { key: 'h1Point', item: 'H1点' },
        { key: 'h3Point', item: 'H3点' },
        { key: 'h6Point', item: 'H6点' },
        { key: 'h12Point', item: 'H12点' },
        { key: 'h24Point', item: 'H24点' },
        { key: 'h1Area', item: 'H1面' },
        { key: 'h3Area', item: 'H3面' },
        { key: 'h6Area', item: 'H6面' },
        { key: 'h12Area', item: 'H12面' },
        { key: 'h24Area', item: 'H24面' }
      ],
      
      // 模态框相关
      modalVisible: false,
      modalConfirmLoading: false,
      modalTitle: '新增设计暴雨',
      isEdit: false,
      editRecord: null,
      
      // 文件上传相关
      fileList: [],
      
      // 表单
      form: this.$form.createForm(this)
    };
  },
  
  mounted() {
    this.loadTestData();
    this.calculateMergeCells();
  },
  
  methods: {
    // 加载测试数据
    loadTestData() {
      // 模拟测试数据
      const testData = [
        {
          id: 1,
          stage: '第一阶段',
          frequency: 'P=3.33%',
          h1Point: '10.50',
          h3Point: '15.20',
          h6Point: '20.10',
          h12Point: '25.30',
          h24Point: '30.40',
          h1Area: '12.30',
          h3Area: '18.70',
          h6Area: '22.90',
          h12Area: '28.10',
          h24Area: '33.60',
          allocation: '暴雨分配文件1.xlsx'
        },
        {
          id: 2,
          stage: '第一阶段',
          frequency: 'P=2%',
          h1Point: '12.30',
          h3Point: '17.80',
          h6Point: '23.40',
          h12Point: '28.90',
          h24Point: '35.20',
          h1Area: '14.20',
          h3Area: '20.50',
          h6Area: '25.80',
          h12Area: '31.70',
          h24Area: '38.90',
          allocation: '暴雨分配文件1.xlsx'
        },
        {
          id: 3,
          stage: '第一阶段',
          frequency: 'P=0.2%',
          h1Point: '15.70',
          h3Point: '22.40',
          h6Point: '28.90',
          h12Point: '35.60',
          h24Point: '42.80',
          h1Area: '18.30',
          h3Area: '25.70',
          h6Area: '32.10',
          h12Area: '39.40',
          h24Area: '47.20',
          allocation: '暴雨分配文件1.xlsx'
        },
        {
          id: 4,
          stage: '第二阶段',
          frequency: 'P=3.33%',
          h1Point: '11.20',
          h3Point: '16.50',
          h6Point: '21.80',
          h12Point: '26.90',
          h24Point: '32.10',
          h1Area: '13.40',
          h3Area: '19.80',
          h6Area: '24.60',
          h12Area: '29.30',
          h24Area: '34.70',
          allocation: '暴雨分配文件2.xlsx'
        },
        {
          id: 5,
          stage: '第二阶段',
          frequency: 'P=2%',
          h1Point: '13.80',
          h3Point: '19.70',
          h6Point: '25.30',
          h12Point: '31.20',
          h24Point: '37.60',
          h1Area: '15.90',
          h3Area: '22.40',
          h6Area: '27.80',
          h12Area: '33.70',
          h24Area: '40.10',
          allocation: '暴雨分配文件2.xlsx'
        },
        {
          id: 6,
          stage: '第二阶段',
          frequency: 'P=0.2%',
          h1Point: '17.40',
          h3Point: '24.80',
          h6Point: '31.70',
          h12Point: '38.90',
          h24Point: '46.30',
          h1Area: '20.10',
          h3Area: '27.90',
          h6Area: '34.20',
          h12Area: '41.80',
          h24Area: '49.60',
          allocation: '暴雨分配文件2.xlsx'
        }
      ];
      
      this.dataSource = testData;
    },
    
    // 显示添加弹窗
    showAddModal() {
      this.modalTitle = '新增设计暴雨';
      this.isEdit = false;
      this.editRecord = null;
      this.fileList = [];
      
      // 初始化参数数据
      this.parameterData = this.parameterData.map(item => ({
        ...item,
        p333: '',
        p2: '',
        p02: ''
      }));
      
      this.modalVisible = true;
      this.$nextTick(() => {
        this.form.resetFields();
      });
    },
    
    // 处理导出
    handleExport() {
      this.$message.info('导出功能待实现');
    },
    
    // 处理编辑
    handleEdit(record) {
      this.modalTitle = '编辑设计暴雨';
      this.isEdit = true;
      this.editRecord = record;
      
      // 设置表单字段值
      this.$nextTick(() => {
        this.form.setFieldsValue({
          stage: record.stage
        });
      });
      
      // 设置参数数据
      this.parameterData = this.parameterData.map(item => {
        return {
          ...item,
          p333: record[`${item.key}`] || '',
          p2: record[`${item.key}`] || '',
          p02: record[`${item.key}`] || ''
        };
      });
      
      // 设置文件列表
      this.fileList = record.allocation ? [{
        uid: '-1',
        name: record.allocation,
        status: 'done'
      }] : [];
      
      this.modalVisible = true;
    },
    
    // 处理删除
    handleDelete(record) {
      this.$message.info('删除功能待实现');
    },
    
    // 处理参数表格变化
    handleParameterChange(value, key, column) {
      const newData = [...this.parameterData];
      const target = newData.find(item => item.key === key);
      if (target) {
        target[column] = value;
        this.parameterData = newData;
      }
    },
    
    // 处理模态框确认
    handleModalOk() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          this.modalConfirmLoading = true;
          
          // 模拟保存操作
          setTimeout(() => {
            this.modalConfirmLoading = false;
            this.modalVisible = false;
            this.$message.success(`${this.isEdit ? '编辑' : '新增'}成功`);
          }, 1000);
        }
      });
    },
    
    // 处理模态框取消
    handleModalCancel() {
      this.modalVisible = false;
    },
    
    // 文件上传前处理
    beforeUpload(file) {
      this.fileList = [...this.fileList, file];
      return false; // 不上传到服务器
    },
    
    // 处理文件移除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
    },
    
    // 计算序号
    calculateIndex(index) {
      // 每个阶段有3行数据，序号每3行递增1
      return Math.floor(index / 3) + 1;
    },
    
    // 计算合并单元格
    calculateMergeCells() {
      const mergeCells = [];
      const data = this.dataSource;
      
      if (!data || data.length === 0) {
        this.mergeCells = mergeCells;
        return;
      }
      
      // 按阶段分组数据
      const groups = [];
      let currentGroup = [];
      let currentStage = data[0].stage;
      
      for (let i = 0; i < data.length; i++) {
        if (data[i].stage === currentStage) {
          currentGroup.push(i);
        } else {
          groups.push(currentGroup);
          currentGroup = [i];
          currentStage = data[i].stage;
        }
      }
      groups.push(currentGroup);
      
      // 为每个阶段组添加合并单元格定义
      let rowIndex = 0;
      for (const group of groups) {
        const rowCount = group.length;
        if (rowCount > 1) {
          // 合并序号列（第0列）
          mergeCells.push({ row: rowIndex, col: 0, rowspan: rowCount, colspan: 1 });
          // 合并阶段列（第1列）
          mergeCells.push({ row: rowIndex, col: 1, rowspan: rowCount, colspan: 1 });
        }
        rowIndex += rowCount;
      }
      
      this.mergeCells = mergeCells;
    }
  }
};

</script>

<style lang="less" scoped>
.design-storm-container {
  padding: 20px;
  background: #fff;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      font-size: 18px;
      font-weight: 600;
    }
    .actions {
      button {
        margin-left: 10px;
      }
    }
  }
  
  .table-container {
    margin-top: 20px;
  }
  
  .parameter-title {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0 10px;
    color: #333;
  }
  
  .allocation-section {
    margin-top: 20px;
    
    .allocation-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #333;
    }
    
    .file-upload {
      margin-top: 10px;
    }
  }
  
  /deep/ .ant-table-thead > tr > th {
    background-color: #f0f2f5;
  }
}
</style>